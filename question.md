# Tetris-Style Block Drawing on Grid
You have a sheet of n × m grid paper and you'd like to draw a cool design on it. You've decided on a block motif similar to tetris pieces. Specifically, your picture will include the following five types of figures:

Types of Figures shown in the screen
A: A single square block.

B: A 1×3 horizontal rectangle (3 blocks wide).

C: A 2×2 square.

D: A vertical T-like shape (3 tall, with one sticking out to the right).

E: A horizontal T-like shape (3 wide, with one sticking up in the center).

## Task
The array figures contains a list of letters representing the types of figures you'd like to include in your design.
Your task is to create a matrix of integers representing the grid paper and draw the figures on it according to the following rules:

## Rules
Start with a matrix of all 0s, and use the 1-based index of each figure to represent it on the grid.
For example, if figures[0] = 'E' then the shape added to the grid will look like this:
```text
  [[0, 1, 0],
   [1, 1, 1]]
   ```

Place the figures on the grid in the order they appear in figures.

Figures must not overlap any other figures that have already been placed.

Figures may not be rotated.

Of all the available locations, choose the one with the lowest row index.

If there are multiple possible locations with the same row, choose the one among them with the lowest column index.

It’s guaranteed that all figures will fit on the grid.

## Output
Return a matrix of integers representing the grid paper after all the figures have been drawn.

## Note
You are not expected to provide the most optimal solution, but a solution with time complexity not worse than O(n.m figures.length) will fit within the execution time limit.


## Add this:
## 📥 Input Format

- `n`: integer — height of the grid  
  - `2 ≤ n ≤ 50`
- `m`: integer — width of the grid  
  - `2 ≤ m ≤ 50`
- `figures`: array of characters  
  - Each character is one of `'A'`, `'B'`, `'C'`, `'D'`, `'E'`  
  - `1 ≤ figures.length ≤ 500`

---

## 📤 Output Format

- Return a 2D integer array of size `n × m`
- Each element is either `0` (empty cell) or the 1-based index of the figure placed there

---

## ✅ Example

### Input

```text
n = 4
m = 4
figures = ['D', 'B', 'A', 'C']

## Example
For n=4 and m=4  and figures = ["D", "B", "A", "C"] 
The output should be 
[[1, 2, 2, 2],
 [1, 1, 3, 0],
 [1, 4, 4, 0],
 [0, 4, 4, 0]]
```

