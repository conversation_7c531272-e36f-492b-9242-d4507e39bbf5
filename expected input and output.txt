for n=4 and m=4  and figures = ["D", "B", "A", "C"] 
The output should be 
[[1, 2, 2, 2],
 [1, 1, 3, 0],
 [1, 4, 4, 0],
 [0, 4, 4, 0]]

 ie 
 solution(n, m, figures) = [[1, 2, 2, 2], 
                            [1, 1, 3, 0], 
                            [1, 4, 4, 0], 
                            [0, 4, 4, 0]]


Test 1:
n = 4
m = 4
figures = ["D", "B", "A", "C"]
solution(n, m, figures) = [[1, 2, 2, 2], 
                            [1, 1, 3, 0], 
                            [1, 4, 4, 0], 
                            [0, 4, 4, 0]]


Test 2:
n = 3
m = 5
figures = ["A", "D", "E"]
solution(n, m, figures) = [[1, 2, 0, 0, 0], 
                            [0, 2, 2, 3, 0],  
                            [0, 2, 3, 3, 3]]


Test 3:
n = 2
m = 2
figures = ["C"]
solution(n, m, figures) = [[1, 1], 
                            [1, 1]]

Test 4:
n = 3
m = 3
figures = ["D", "A", "A", "A", "A", "A" ]
solution(n, m, figures) = [[1, 2, 3], 
                            [1, 1, 4],  
                            [1, 5, 6]]


Test 5:
n = 5
m = 5
figures = ["A", "A", "A", "A", "A", "A", "A", "A", "A", "A"]
solution(n, m, figures) = [[1, 2, 3, 4, 5], 
                            [6, 7, 8, 9, 19],  
                            [0, 0, 0, 0, 0],
                            [0, 0, 0, 0, 0],
                            [0, 0, 0, 0, 0]]



# uncomplete Test 7:
n = 5
m = 10
figures = ["B", "C", "C", "C", "C" ]
solution(n, m, figures) = [[1, 1, 1, 2, 2, 3, 3, 4, 4, 0], 
                            [5, 5, 0, 2, 2, 3, 3, 4, 4, 0],
                            [5, 5, 0, 0, 0, 0, 0, 0, 0, 0],  
                            [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]

                          
                    

